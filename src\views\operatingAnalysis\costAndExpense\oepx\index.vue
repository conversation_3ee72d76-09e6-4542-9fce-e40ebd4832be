<template>
  <div class="oepx">
    <div class="content-up">
      <div class="main-indicators">
        <chartBox :title="'主要指标总览'">
          <template v-slot:box-right>
            <DatePicker
              v-model="newDateValue"
              dateType="day"
              @change="logChange('新日期选择', $event)"
            />
          </template>
          <CarouselBtn :buttons="buttons" />
        </chartBox>
      </div>
      <div class="statistics-box">
        <chartBox :title="'分项统计'">
          <div class="table-box">
            <CommonTable
              :tableData="tableData"
              :colums="colums"
              :showIndexColumn="false"
              :border="false"
            />
          </div>
        </chartBox>
      </div>
    </div>
    <div class="content-down">
      <div class="cost-structure">
        <chartBox :title="'费用结构'"> </chartBox>
      </div>
      <div class="motivation-box">
        <chartBox :title="'同比增减动因'"> </chartBox>
      </div>
      <div class="analysis-box">
        <chartBox :title="'分油气田分析'"> </chartBox>
      </div>
    </div>
  </div>
</template>

<script>
import CommonTable from "@/components/comTable/commonTable.vue";
import CarouselBtn from "../../components/CarouselBtn.vue";
export default {
  name: "Oepx",
  components: { CommonTable, CarouselBtn },
  data() {
    return {
      buttons: [
        "作业公司",
        "YC13-1",
        "YC13-10",
        "LS25-1",
        "LS17-2",
        "文昌16-2",
      ],
      colums: [
        {
          label: "科目",
          prop: "subject",
        },
        {
          label: "同期完成",
          children: [
            {
              label: "本年累计",
              prop: "thisYearTotal",
            },
            {
              label: "同期预算",
              prop: "lastYearTotal",
            },
            {
              label: "差额",
              prop: "difference",
            },
            {
              label: "同期完成率",
              prop: "completionRate",
            },
          ],
        },
        {
          label: "全年完成",
          children: [
            {
              label: "去年预算",
              prop: "lastYearTotal",
            },
            {
              label: "全年完成率",
              prop: "completionRateYear",
            },
          ],
        },
      ],
      tableData: [
        {
          subject: "科目1",
          thisYearTotal: "100",
          lastYearTotal: "100",
          difference: "100",
          completionRate: "100",
          lastYearTotal: "100",
          completionRateYear: "100",
        },
        {
          subject: "科目2",
          thisYearTotal: "100",
          lastYearTotal: "100",
          difference: "100",
          completionRate: "100",
          lastYearTotal: "100",
          completionRateYear: "100",
        },
      ],
    };
  },
};
</script>
<style lang="scss" scoped>
.oepx {
  .content-up {
    display: flex;
    justify-content: space-between;

    .main-indicators {
      flex: 1;
      min-width: 0;
      margin-right: 10px;
    }

    .statistics-box {
      flex: 1;
      min-width: 0;

      .table-box {
        margin: 12px 16px; // 减少上下边距
        flex: 1;
        overflow: visible; // 移除滚动条，让表格完整显示
        display: flex;
        flex-direction: column;
      }
    }
  }
}
</style>
